-- 推流请求耗时均值 - AppID过滤查询
-- 生成时间: 2025-08-05 16:16:10
-- 总计 6 个 SQL 语句

-- 746_推流请求耗时均值_默认指标_1day_AppID=3575801176
SELECT
    timestamp,
    sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176'
GROUP BY timestamp
ORDER BY timestamp

-- 748_推流请求耗时均值_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯'
GROUP BY timestamp
ORDER BY timestamp

-- 750_推流请求耗时均值_默认指标_1day_AppID=3575801176_Where=platformeqNative
SELECT
    timestamp,
    sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176' AND (platform='Native')
GROUP BY timestamp
ORDER BY timestamp

-- 752_推流请求耗时均值_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqNative
SELECT
    timestamp,
    sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native')
GROUP BY timestamp
ORDER BY timestamp

-- 754_推流请求耗时均值_默认指标_1day_AppID=3575801176_Where=platformeqNative_AND_os_typeeqAndroid
SELECT
    timestamp,
    sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176' AND (platform='Native' AND os_type='Android')
GROUP BY timestamp
ORDER BY timestamp

-- 756_推流请求耗时均值_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqNative_AND_os_typeeqAndroid
SELECT
    timestamp,
    sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native' AND os_type='Android')
GROUP BY timestamp
ORDER BY timestamp

