-- 推流请求耗时均值 - 常规查询
-- 生成时间: 2025-08-05 16:16:10
-- 总计 12 个 SQL 语句

-- 745_推流请求耗时均值_默认指标_1day_NoFilter
SELECT
    timestamp,
    sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 747_推流请求耗时均值_默认指标_1day_Country=中国
SELECT
    timestamp,
    sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国'
GROUP BY timestamp
ORDER BY timestamp

-- 749_推流请求耗时均值_默认指标_1day_Where=platformeqNative
SELECT
    timestamp,
    sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND (platform='Native')
GROUP BY timestamp
ORDER BY timestamp

-- 751_推流请求耗时均值_默认指标_1day_Country=中国_Where=platformeqNative
SELECT
    timestamp,
    sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='Native')
GROUP BY timestamp
ORDER BY timestamp

-- 753_推流请求耗时均值_默认指标_1day_Where=platformeqNative_AND_os_typeeqAndroid
SELECT
    timestamp,
    sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND (platform='Native' AND os_type='Android')
GROUP BY timestamp
ORDER BY timestamp

-- 755_推流请求耗时均值_默认指标_1day_Country=中国_Where=platformeqNative_AND_os_typeeqAndroid
SELECT
    timestamp,
    sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='Native' AND os_type='Android')
GROUP BY timestamp
ORDER BY timestamp

-- 763_推流请求耗时均值_默认指标_1day_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 764_推流请求耗时均值_默认指标_1day_Country=中国_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 765_推流请求耗时均值_默认指标_1day_Dig=app_id_Where=platformeqNative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND (platform='Native')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 766_推流请求耗时均值_默认指标_1day_Country=中国_Dig=app_id_Where=platformeqNative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='Native')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 767_推流请求耗时均值_默认指标_1day_Dig=app_id_Where=platformeqNative_AND_os_typeeqAndroid
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND (platform='Native' AND os_type='Android')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 768_推流请求耗时均值_默认指标_1day_Country=中国_Dig=app_id_Where=platformeqNative_AND_os_typeeqAndroid
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='Native' AND os_type='Android')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

