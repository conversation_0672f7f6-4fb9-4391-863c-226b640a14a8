-- 统一接入request成功率 - AppID过滤查询
-- 生成时间: 2025-08-05 16:16:10
-- 总计 6 个 SQL 语句

-- 266_统一接入request成功率_默认指标_1day_AppID=3575801176
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3575801176'
GROUP BY timestamp
ORDER BY timestamp

-- 268_统一接入request成功率_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯'
GROUP BY timestamp
ORDER BY timestamp

-- 270_统一接入request成功率_默认指标_1day_AppID=3575801176_Where=platformeqNative
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native')
GROUP BY timestamp
ORDER BY timestamp

-- 272_统一接入request成功率_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqNative
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native')
GROUP BY timestamp
ORDER BY timestamp

-- 274_统一接入request成功率_默认指标_1day_AppID=3575801176_Where=platformeqNative_AND_os_typeeqAndroid
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native' AND os_type='Android')
GROUP BY timestamp
ORDER BY timestamp

-- 276_统一接入request成功率_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqNative_AND_os_typeeqAndroid
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native' AND os_type='Android')
GROUP BY timestamp
ORDER BY timestamp

