-- 统一接入request成功率 - 错误码分布查询
-- 生成时间: 2025-08-05 16:16:10
-- 总计 24 个 SQL 语句

-- 289_统一接入request成功率_错误码分布_1day_NoFilter
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 290_统一接入request成功率_错误码分布_1day_AppID=3575801176
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 291_统一接入request成功率_错误码分布_1day_Country=中国
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND country = '中国' AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND country = '中国' AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 292_统一接入request成功率_错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 293_统一接入request成功率_错误码分布_1day_Where=platformeqNative
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND (platform='Native') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND (platform='Native') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 294_统一接入request成功率_错误码分布_1day_AppID=3575801176_Where=platformeqNative
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 295_统一接入request成功率_错误码分布_1day_Country=中国_Where=platformeqNative
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='Native') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='Native') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 296_统一接入request成功率_错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqNative
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 297_统一接入request成功率_错误码分布_1day_Where=platformeqNative_AND_os_typeeqAndroid
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND (platform='Native' AND os_type='Android') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND (platform='Native' AND os_type='Android') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 298_统一接入request成功率_错误码分布_1day_AppID=3575801176_Where=platformeqNative_AND_os_typeeqAndroid
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native' AND os_type='Android') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native' AND os_type='Android') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 299_统一接入request成功率_错误码分布_1day_Country=中国_Where=platformeqNative_AND_os_typeeqAndroid
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='Native' AND os_type='Android') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='Native' AND os_type='Android') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 300_统一接入request成功率_错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqNative_AND_os_typeeqAndroid
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native' AND os_type='Android') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native' AND os_type='Android') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 301_统一接入request成功率_错误码分布_1day_Dig=country
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 302_统一接入request成功率_错误码分布_1day_AppID=3575801176_Dig=country
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 303_统一接入request成功率_错误码分布_1day_Dig=country_Where=platformeqNative
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND (platform='Native') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 304_统一接入request成功率_错误码分布_1day_AppID=3575801176_Dig=country_Where=platformeqNative
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 305_统一接入request成功率_错误码分布_1day_Dig=country_Where=platformeqNative_AND_os_typeeqAndroid
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND (platform='Native' AND os_type='Android') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 306_统一接入request成功率_错误码分布_1day_AppID=3575801176_Dig=country_Where=platformeqNative_AND_os_typeeqAndroid
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native' AND os_type='Android') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 307_统一接入request成功率_错误码分布_1day_Dig=app_id
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 308_统一接入request成功率_错误码分布_1day_Country=中国_Dig=app_id
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND country = '中国' AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 309_统一接入request成功率_错误码分布_1day_Dig=app_id_Where=platformeqNative
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND (platform='Native') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 310_统一接入request成功率_错误码分布_1day_Country=中国_Dig=app_id_Where=platformeqNative
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='Native') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 311_统一接入request成功率_错误码分布_1day_Dig=app_id_Where=platformeqNative_AND_os_typeeqAndroid
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND (platform='Native' AND os_type='Android') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 312_统一接入request成功率_错误码分布_1day_Country=中国_Dig=app_id_Where=platformeqNative_AND_os_typeeqAndroid
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='Native' AND os_type='Android') AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

