-- 视频首帧 - 常规查询
-- 生成时间: 2025-08-05 16:16:10
-- 总计 12 个 SQL 语句

-- 697_视频首帧_默认指标_1day_NoFilter
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 699_视频首帧_默认指标_1day_Country=中国
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国'
GROUP BY timestamp
ORDER BY timestamp

-- 701_视频首帧_默认指标_1day_Where=platformeqNative
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND (platform='Native')
GROUP BY timestamp
ORDER BY timestamp

-- 703_视频首帧_默认指标_1day_Country=中国_Where=platformeqNative
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国' AND (platform='Native')
GROUP BY timestamp
ORDER BY timestamp

-- 705_视频首帧_默认指标_1day_Where=platformeqNative_AND_os_typeeqAndroid
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND (platform='Native' AND os_type='Android')
GROUP BY timestamp
ORDER BY timestamp

-- 707_视频首帧_默认指标_1day_Country=中国_Where=platformeqNative_AND_os_typeeqAndroid
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国' AND (platform='Native' AND os_type='Android')
GROUP BY timestamp
ORDER BY timestamp

-- 715_视频首帧_默认指标_1day_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 716_视频首帧_默认指标_1day_Country=中国_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 717_视频首帧_默认指标_1day_Dig=app_id_Where=platformeqNative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND (platform='Native')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 718_视频首帧_默认指标_1day_Country=中国_Dig=app_id_Where=platformeqNative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国' AND (platform='Native')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 719_视频首帧_默认指标_1day_Dig=app_id_Where=platformeqNative_AND_os_typeeqAndroid
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND (platform='Native' AND os_type='Android')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 720_视频首帧_默认指标_1day_Country=中国_Dig=app_id_Where=platformeqNative_AND_os_typeeqAndroid
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国' AND (platform='Native' AND os_type='Android')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

