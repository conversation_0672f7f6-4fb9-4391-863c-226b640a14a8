-- 视频首帧 - AppID过滤查询
-- 生成时间: 2025-08-05 16:16:10
-- 总计 6 个 SQL 语句

-- 698_视频首帧_默认指标_1day_AppID=3575801176
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3575801176'
GROUP BY timestamp
ORDER BY timestamp

-- 700_视频首帧_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯'
GROUP BY timestamp
ORDER BY timestamp

-- 702_视频首帧_默认指标_1day_AppID=3575801176_Where=platformeqNative
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3575801176' AND (platform='Native')
GROUP BY timestamp
ORDER BY timestamp

-- 704_视频首帧_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqNative
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native')
GROUP BY timestamp
ORDER BY timestamp

-- 706_视频首帧_默认指标_1day_AppID=3575801176_Where=platformeqNative_AND_os_typeeqAndroid
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3575801176' AND (platform='Native' AND os_type='Android')
GROUP BY timestamp
ORDER BY timestamp

-- 708_视频首帧_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqNative_AND_os_typeeqAndroid
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native' AND os_type='Android')
GROUP BY timestamp
ORDER BY timestamp

