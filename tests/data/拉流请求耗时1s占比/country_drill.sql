-- 拉流请求耗时1s占比 - 国家下钻查询
-- 生成时间: 2025-08-05 16:16:10
-- 总计 3 个 SQL 语句

-- 781_拉流请求耗时1s占比_默认指标_1day_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 783_拉流请求耗时1s占比_默认指标_1day_Dig=country_Where=platformeqNative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND (platform='Native')
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 785_拉流请求耗时1s占比_默认指标_1day_Dig=country_Where=platformeqNative_AND_os_typeeqAndroid
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND (platform='Native' AND os_type='Android')
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

