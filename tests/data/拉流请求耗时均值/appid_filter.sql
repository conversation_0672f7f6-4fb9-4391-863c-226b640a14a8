-- 拉流请求耗时均值 - AppID过滤查询
-- 生成时间: 2025-08-05 16:16:10
-- 总计 6 个 SQL 语句

-- 722_拉流请求耗时均值_默认指标_1day_AppID=3575801176
SELECT
    timestamp,
    sum(play_request_num)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176'
GROUP BY timestamp
ORDER BY timestamp

-- 724_拉流请求耗时均值_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    sum(play_request_num)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯'
GROUP BY timestamp
ORDER BY timestamp

-- 726_拉流请求耗时均值_默认指标_1day_AppID=3575801176_Where=platformeqNative
SELECT
    timestamp,
    sum(play_request_num)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176' AND (platform='Native')
GROUP BY timestamp
ORDER BY timestamp

-- 728_拉流请求耗时均值_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqNative
SELECT
    timestamp,
    sum(play_request_num)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native')
GROUP BY timestamp
ORDER BY timestamp

-- 730_拉流请求耗时均值_默认指标_1day_AppID=3575801176_Where=platformeqNative_AND_os_typeeqAndroid
SELECT
    timestamp,
    sum(play_request_num)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176' AND (platform='Native' AND os_type='Android')
GROUP BY timestamp
ORDER BY timestamp

-- 732_拉流请求耗时均值_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqNative_AND_os_typeeqAndroid
SELECT
    timestamp,
    sum(play_request_num)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native' AND os_type='Android')
GROUP BY timestamp
ORDER BY timestamp

