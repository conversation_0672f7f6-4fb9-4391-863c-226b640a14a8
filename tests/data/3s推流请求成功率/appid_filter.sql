-- 3s推流请求成功率 - AppID过滤查询
-- 生成时间: 2025-08-05 16:16:10
-- 总计 6 个 SQL 语句

-- 410_3s推流请求成功率_默认指标_1day_AppID=3575801176
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12301011,52001012,52001105) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176'
GROUP BY timestamp
ORDER BY timestamp

-- 412_3s推流请求成功率_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12301011,52001012,52001105) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯'
GROUP BY timestamp
ORDER BY timestamp

-- 414_3s推流请求成功率_默认指标_1day_AppID=3575801176_Where=platformeqNative
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12301011,52001012,52001105) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native')
GROUP BY timestamp
ORDER BY timestamp

-- 416_3s推流请求成功率_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqNative
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12301011,52001012,52001105) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native')
GROUP BY timestamp
ORDER BY timestamp

-- 418_3s推流请求成功率_默认指标_1day_AppID=3575801176_Where=platformeqNative_AND_os_typeeqAndroid
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12301011,52001012,52001105) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native' AND os_type='Android')
GROUP BY timestamp
ORDER BY timestamp

-- 420_3s推流请求成功率_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqNative_AND_os_typeeqAndroid
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12301011,52001012,52001105) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native' AND os_type='Android')
GROUP BY timestamp
ORDER BY timestamp

