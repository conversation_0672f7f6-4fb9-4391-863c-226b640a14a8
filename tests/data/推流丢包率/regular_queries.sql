-- 推流丢包率 - 常规查询
-- 生成时间: 2025-08-05 16:16:10
-- 总计 12 个 SQL 语句

-- 241_推流丢包率_默认指标_1day_NoFilter
SELECT
    timestamp,
    ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
    SUM(plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW()
GROUP BY timestamp
ORDER BY timestamp

-- 243_推流丢包率_默认指标_1day_Country=中国
SELECT
    timestamp,
    ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
    SUM(plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国'
GROUP BY timestamp
ORDER BY timestamp

-- 245_推流丢包率_默认指标_1day_Where=platformeqNative
SELECT
    timestamp,
    ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
    SUM(plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND (platform='Native')
GROUP BY timestamp
ORDER BY timestamp

-- 247_推流丢包率_默认指标_1day_Country=中国_Where=platformeqNative
SELECT
    timestamp,
    ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
    SUM(plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国' AND (platform='Native')
GROUP BY timestamp
ORDER BY timestamp

-- 249_推流丢包率_默认指标_1day_Where=platformeqNative_AND_os_typeeqAndroid
SELECT
    timestamp,
    ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
    SUM(plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND (platform='Native' AND os_type='Android')
GROUP BY timestamp
ORDER BY timestamp

-- 251_推流丢包率_默认指标_1day_Country=中国_Where=platformeqNative_AND_os_typeeqAndroid
SELECT
    timestamp,
    ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
    SUM(plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国' AND (platform='Native' AND os_type='Android')
GROUP BY timestamp
ORDER BY timestamp

-- 259_推流丢包率_默认指标_1day_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
        SUM(plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW()
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 260_推流丢包率_默认指标_1day_Country=中国_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
        SUM(plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 261_推流丢包率_默认指标_1day_Dig=app_id_Where=platformeqNative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
        SUM(plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND (platform='Native')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 262_推流丢包率_默认指标_1day_Country=中国_Dig=app_id_Where=platformeqNative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
        SUM(plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国' AND (platform='Native')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 263_推流丢包率_默认指标_1day_Dig=app_id_Where=platformeqNative_AND_os_typeeqAndroid
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
        SUM(plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND (platform='Native' AND os_type='Android')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 264_推流丢包率_默认指标_1day_Country=中国_Dig=app_id_Where=platformeqNative_AND_os_typeeqAndroid
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
        SUM(plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国' AND (platform='Native' AND os_type='Android')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

