-- 拉流l3子事件成功率 - 错误码分布查询
-- 生成时间: 2025-08-05 16:16:10
-- 总计 24 个 SQL 语句

-- 577_拉流l3子事件成功率_错误码分布_1day_NoFilter
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 578_拉流l3子事件成功率_错误码分布_1day_AppID=3575801176
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 579_拉流l3子事件成功率_错误码分布_1day_Country=中国
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND country = '中国' AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND country = '中国' AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 580_拉流l3子事件成功率_错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 581_拉流l3子事件成功率_错误码分布_1day_Where=platformeqNative
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND (platform='Native') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND (platform='Native') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 582_拉流l3子事件成功率_错误码分布_1day_AppID=3575801176_Where=platformeqNative
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 583_拉流l3子事件成功率_错误码分布_1day_Country=中国_Where=platformeqNative
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='Native') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='Native') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 584_拉流l3子事件成功率_错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqNative
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 585_拉流l3子事件成功率_错误码分布_1day_Where=platformeqNative_AND_os_typeeqAndroid
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 586_拉流l3子事件成功率_错误码分布_1day_AppID=3575801176_Where=platformeqNative_AND_os_typeeqAndroid
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 587_拉流l3子事件成功率_错误码分布_1day_Country=中国_Where=platformeqNative_AND_os_typeeqAndroid
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 588_拉流l3子事件成功率_错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqNative_AND_os_typeeqAndroid
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 589_拉流l3子事件成功率_错误码分布_1day_Dig=country
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 590_拉流l3子事件成功率_错误码分布_1day_AppID=3575801176_Dig=country
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 591_拉流l3子事件成功率_错误码分布_1day_Dig=country_Where=platformeqNative
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND (platform='Native') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 592_拉流l3子事件成功率_错误码分布_1day_AppID=3575801176_Dig=country_Where=platformeqNative
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 593_拉流l3子事件成功率_错误码分布_1day_Dig=country_Where=platformeqNative_AND_os_typeeqAndroid
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 594_拉流l3子事件成功率_错误码分布_1day_AppID=3575801176_Dig=country_Where=platformeqNative_AND_os_typeeqAndroid
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 595_拉流l3子事件成功率_错误码分布_1day_Dig=app_id
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 596_拉流l3子事件成功率_错误码分布_1day_Country=中国_Dig=app_id
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND country = '中国' AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 597_拉流l3子事件成功率_错误码分布_1day_Dig=app_id_Where=platformeqNative
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND (platform='Native') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 598_拉流l3子事件成功率_错误码分布_1day_Country=中国_Dig=app_id_Where=platformeqNative
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='Native') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 599_拉流l3子事件成功率_错误码分布_1day_Dig=app_id_Where=platformeqNative_AND_os_typeeqAndroid
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 600_拉流l3子事件成功率_错误码分布_1day_Country=中国_Dig=app_id_Where=platformeqNative_AND_os_typeeqAndroid
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

