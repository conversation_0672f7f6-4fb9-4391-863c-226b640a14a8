-- 登录成功率 - 常规查询
-- 生成时间: 2025-08-05 16:16:10
-- 总计 12 个 SQL 语句

-- 049_登录成功率_默认指标_1day_NoFilter
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
GROUP BY timestamp
ORDER BY timestamp

-- 051_登录成功率_默认指标_1day_Country=中国
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国'
GROUP BY timestamp
ORDER BY timestamp

-- 053_登录成功率_默认指标_1day_Where=platformeqNative
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND (platform='Native')
GROUP BY timestamp
ORDER BY timestamp

-- 055_登录成功率_默认指标_1day_Country=中国_Where=platformeqNative
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND (platform='Native')
GROUP BY timestamp
ORDER BY timestamp

-- 057_登录成功率_默认指标_1day_Where=platformeqNative_AND_os_typeeqAndroid
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND (platform='Native' AND os_type='Android')
GROUP BY timestamp
ORDER BY timestamp

-- 059_登录成功率_默认指标_1day_Country=中国_Where=platformeqNative_AND_os_typeeqAndroid
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND (platform='Native' AND os_type='Android')
GROUP BY timestamp
ORDER BY timestamp

-- 067_登录成功率_默认指标_1day_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 068_登录成功率_默认指标_1day_Country=中国_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 069_登录成功率_默认指标_1day_Dig=app_id_Where=platformeqNative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND (platform='Native')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 070_登录成功率_默认指标_1day_Country=中国_Dig=app_id_Where=platformeqNative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND (platform='Native')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 071_登录成功率_默认指标_1day_Dig=app_id_Where=platformeqNative_AND_os_typeeqAndroid
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND (platform='Native' AND os_type='Android')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 072_登录成功率_默认指标_1day_Country=中国_Dig=app_id_Where=platformeqNative_AND_os_typeeqAndroid
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND (platform='Native' AND os_type='Android')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

