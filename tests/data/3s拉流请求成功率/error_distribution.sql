-- 3s拉流请求成功率 - 错误码分布查询
-- 生成时间: 2025-08-05 16:16:10
-- 总计 24 个 SQL 语句

-- 385_3s拉流请求成功率_错误码分布_1day_NoFilter
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 386_3s拉流请求成功率_错误码分布_1day_AppID=3575801176
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 387_3s拉流请求成功率_错误码分布_1day_Country=中国
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国' AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国' AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 388_3s拉流请求成功率_错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 389_3s拉流请求成功率_错误码分布_1day_Where=platformeqNative
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND (platform='Native') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND (platform='Native') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 390_3s拉流请求成功率_错误码分布_1day_AppID=3575801176_Where=platformeqNative
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 391_3s拉流请求成功率_错误码分布_1day_Country=中国_Where=platformeqNative
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='Native') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='Native') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 392_3s拉流请求成功率_错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqNative
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 393_3s拉流请求成功率_错误码分布_1day_Where=platformeqNative_AND_os_typeeqAndroid
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 394_3s拉流请求成功率_错误码分布_1day_AppID=3575801176_Where=platformeqNative_AND_os_typeeqAndroid
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 395_3s拉流请求成功率_错误码分布_1day_Country=中国_Where=platformeqNative_AND_os_typeeqAndroid
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 396_3s拉流请求成功率_错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqNative_AND_os_typeeqAndroid
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 397_3s拉流请求成功率_错误码分布_1day_Dig=country
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 398_3s拉流请求成功率_错误码分布_1day_AppID=3575801176_Dig=country
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 399_3s拉流请求成功率_错误码分布_1day_Dig=country_Where=platformeqNative
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND (platform='Native') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 400_3s拉流请求成功率_错误码分布_1day_AppID=3575801176_Dig=country_Where=platformeqNative
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 401_3s拉流请求成功率_错误码分布_1day_Dig=country_Where=platformeqNative_AND_os_typeeqAndroid
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 402_3s拉流请求成功率_错误码分布_1day_AppID=3575801176_Dig=country_Where=platformeqNative_AND_os_typeeqAndroid
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 403_3s拉流请求成功率_错误码分布_1day_Dig=app_id
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 404_3s拉流请求成功率_错误码分布_1day_Country=中国_Dig=app_id
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国' AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 405_3s拉流请求成功率_错误码分布_1day_Dig=app_id_Where=platformeqNative
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND (platform='Native') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 406_3s拉流请求成功率_错误码分布_1day_Country=中国_Dig=app_id_Where=platformeqNative
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='Native') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 407_3s拉流请求成功率_错误码分布_1day_Dig=app_id_Where=platformeqNative_AND_os_typeeqAndroid
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 408_3s拉流请求成功率_错误码分布_1day_Country=中国_Dig=app_id_Where=platformeqNative_AND_os_typeeqAndroid
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='Native' AND os_type='Android') AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

