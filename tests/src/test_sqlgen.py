"""
SQL 生成器测试工具

功能：
- 生成日级别 SQL 语句
- 全量 SQL 生成和对比测试
"""

import argparse
import os
import re
import sqlite3
import sys
import unittest
from datetime import datetime, timedelta
from typing import Dict, Tuple

import sqlparse

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.zego_tools.sql_generator import DataQueryParams, generate_sql
from src.zego_tools.sql_generator.metrics import metrics_registry


def get_data_dir() -> str:
    """获取数据目录路径"""
    return os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "data")


def ensure_data_dir():
    """确保数据目录存在"""
    data_dir = get_data_dir()
    os.makedirs(data_dir, exist_ok=True)
    return data_dir


def normalize_sql(sql: str) -> str:
    """
    标准化SQL语句，用于更准确的比较
    
    Args:
        sql: 原始SQL语句
        
    Returns:
        标准化后的SQL语句
    """
    try:
        # 使用sqlparse进行格式化
        formatted = sqlparse.format(
            sql, 
            reindent=True, 
            strip_comments=True,
            strip_whitespace=True
        ).strip()
        return formatted
    except Exception:
        # 如果sqlparse失败，返回清理后的原始SQL
        return ' '.join(sql.split())


def compare_sql_strings(sql1: str, sql2: str) -> Tuple[bool, str]:
    """
    比较两个SQL字符串是否相等
    
    Args:
        sql1: 第一个SQL字符串
        sql2: 第二个SQL字符串
        
    Returns:
        (是否相等, 差异描述)
    """
    # 首先进行简单字符串比较
    if sql1 == sql2:
        return True, ""
    
    # 如果简单比较失败，使用sqlparse进行进阶检查
    try:
        normalized1 = normalize_sql(sql1)
        normalized2 = normalize_sql(sql2)
        
        if normalized1 == normalized2:
            return True, "格式差异（语义相同）"
        else:
            return False, "SQL语义不同"
    except Exception as e:
        return False, f"SQL解析失败: {e}"


def create_relative_time_sql(sql: str) -> str:
    """
    将 SQL 中的绝对时间转换为相对时间格式（日级别）

    Args:
        sql: 原始 SQL 语句

    Returns:
        转换后的 SQL 语句
    """
    # 日级别使用10天间隔
    interval = "10 DAY"

    # 替换 BETWEEN ... AND ... 的时间模式
    between_pattern = (
        r"timestamp BETWEEN '(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})' AND '(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})'"
    )

    def replace_between(match):
        return f"timestamp BETWEEN DATE_SUB(NOW(), INTERVAL {interval}) AND NOW()"

    sql = re.sub(between_pattern, replace_between, sql)
    return sql


def generate_daily_sql() -> Dict[str, str]:
    """
    生成日级别 SQL 语句

    Returns:
        字典，键为描述，值为 SQL 语句
    """
    sql_combinations = {}

    # 获取所有已注册的指标
    all_metrics = metrics_registry.list_all()
    print(f"发现 {len(all_metrics)} 个指标:")
    for metric_name in all_metrics.keys():
        print(f"  - {metric_name}")

    # 支持的指标类型
    metric_types = [
        "默认指标",
        "错误码分布",
    ]

    # 测试下钻维度
    test_dimensions = [
        None,  # 普通趋势分析
        "country",  # 国家下钻
        "app_id",  # 客户下钻
    ]

    # 测试WHERE条件
    test_where_conditions = [
        None,  # 无额外条件
        "platform='Native'",  # 平台过滤
        "platform='Native' AND os_type='Android'",  # 复合条件
    ]

    # 测试过滤条件
    test_filters = [
        {"appid_filter": None, "country_filter": None},  # 无过滤
        {"appid_filter": 3575801176, "country_filter": None},  # AppID过滤
        {"appid_filter": None, "country_filter": "中国"},  # 国家过滤
        {"appid_filter": 3206531758, "country_filter": "沙特阿拉伯"},  # 双重过滤
    ]

    # 日级别时间范围（10天）
    time_range = timedelta(days=10)
    counter = 0

    for metric_name in all_metrics.keys():
        for metric_type in metric_types:
            for digdimension in test_dimensions:
                for where_condition in test_where_conditions:
                    # 错误码分布只支持成功率类型指标
                    if metric_type == "错误码分布":
                        metric = all_metrics[metric_name]
                        if not metric.supports_error_distribution:
                            continue

                    for filter_config in test_filters:
                        # 优化逻辑：跳过不必要的维度下钻分析
                        if digdimension == "country" and filter_config.get("country_filter"):
                            # 已经指定了country，跳过国家维度下钻
                            continue
                        if digdimension == "app_id" and filter_config.get("appid_filter"):
                            # 已经指定了appid，跳过客户维度下钻
                            continue

                        counter += 1

                        # 计算时间范围
                        end_time = datetime.now()
                        start_time = end_time - time_range

                        # 创建查询参数
                        params = DataQueryParams(
                            metric_name=metric_name,
                            metric_type=metric_type,
                            digdimension=digdimension,
                            where=where_condition,
                            time_start=start_time.strftime("%Y-%m-%d %H:%M:%S"),
                            time_end=end_time.strftime("%Y-%m-%d %H:%M:%S"),
                            appid_filter=filter_config["appid_filter"],
                            country_filter=filter_config["country_filter"],
                        )

                        try:
                            # 生成 SQL
                            sql = generate_sql(params)

                            # 转换为相对时间格式
                            relative_sql = create_relative_time_sql(sql)

                            # 生成描述性键名
                            filter_desc = []
                            if filter_config["appid_filter"]:
                                filter_desc.append(f"AppID={filter_config['appid_filter']}")
                            if filter_config["country_filter"]:
                                filter_desc.append(f"Country={filter_config['country_filter']}")
                            if digdimension:
                                filter_desc.append(f"Dig={digdimension}")
                            if where_condition:
                                filter_desc.append(
                                    f"Where={where_condition.replace(' ', '_').replace('=', 'eq').replace('\'', '')}"
                                )
                            filter_str = "_" + "_".join(filter_desc) if filter_desc else "_NoFilter"

                            key = f"{counter:03d}_{metric_name}_{metric_type}_1day{filter_str}"
                            sql_combinations[key] = relative_sql

                            print(f"✓ 生成 SQL: {key}")

                        except Exception as e:
                            print(
                                f"✗ 生成失败: {metric_name} - {metric_type} - digdimension={digdimension} - where={where_condition} - {filter_config}: {e}"
                            )

    return sql_combinations


def generate_all_sql_combinations() -> Dict[str, str]:
    """
    生成所有可能的 SQL 组合（全量测试用）

    Returns:
        字典，键为描述，值为 SQL 语句
    """
    sql_combinations = {}

    # 获取所有已注册的指标
    all_metrics = metrics_registry.list_all()
    print(f"发现 {len(all_metrics)} 个指标:")
    for metric_name in all_metrics.keys():
        print(f"  - {metric_name}")

    # 支持的指标类型
    metric_types = [
        "默认指标",
        "错误码分布",
    ]

    # 测试下钻维度
    test_dimensions = [
        None,  # 普通趋势分析
        "sdk_version",  # SDK版本下钻
        "country",  # 国家下钻
        "isp",  # 运营商下钻
        "app_id",  # 客户下钻
    ]

    # 测试WHERE条件（简化版本，避免生成过多组合）
    test_where_conditions = [
        None,  # 无额外条件
        "platform='Native'",  # 平台过滤
    ]

    # 测试过滤条件
    test_filters = [
        {"appid_filter": None, "country_filter": None},  # 无过滤
        {"appid_filter": 3575801176, "country_filter": None},  # AppID过滤
        {"appid_filter": None, "country_filter": "中国"},  # 国家过滤
        {"appid_filter": 3206531758, "country_filter": "沙特阿拉伯"},  # 双重过滤
    ]

    # 不同的时间粒度对应的时间范围
    time_granularities = [
        ("1min", timedelta(minutes=30)),  # 30分钟，对应1分钟粒度
        ("10min", timedelta(hours=6)),  # 6小时，对应10分钟粒度
        ("1hour", timedelta(days=2)),  # 2天，对应1小时粒度
        ("1day", timedelta(days=10)),  # 10天，对应1天粒度
    ]

    counter = 0

    for metric_name in all_metrics.keys():
        for metric_type in metric_types:
            for digdimension in test_dimensions:
                for where_condition in test_where_conditions:
                    # 错误码分布只支持成功率类型指标
                    if metric_type == "错误码分布":
                        metric = all_metrics[metric_name]
                        if not metric.supports_error_distribution:
                            continue

                    for granularity, time_range in time_granularities:
                        for filter_config in test_filters:
                            # 优化逻辑：跳过不必要的维度下钻分析
                            if digdimension == "country" and filter_config.get("country_filter"):
                                # 已经指定了country，跳过国家维度下钻
                                continue
                            if digdimension == "app_id" and filter_config.get("appid_filter"):
                                # 已经指定了appid，跳过客户维度下钻
                                continue

                            counter += 1

                            # 计算时间范围
                            end_time = datetime.now()
                            start_time = end_time - time_range

                            # 创建查询参数
                            params = DataQueryParams(
                                metric_name=metric_name,
                                metric_type=metric_type,
                                digdimension=digdimension,
                                where=where_condition,
                                time_start=start_time.strftime("%Y-%m-%d %H:%M:%S"),
                                time_end=end_time.strftime("%Y-%m-%d %H:%M:%S"),
                                appid_filter=filter_config["appid_filter"],
                                country_filter=filter_config["country_filter"],
                            )

                        try:
                            # 生成 SQL
                            sql = generate_sql(params)

                            # 转换为相对时间格式
                            if granularity == "1day":
                                relative_sql = create_relative_time_sql(sql)
                            else:
                                # 其他粒度保持原有逻辑
                                time_intervals = {"1min": "10 MINUTE", "10min": "100 MINUTE", "1hour": "10 HOUR"}
                                interval = time_intervals.get(granularity, "30 DAY")
                                between_pattern = r"timestamp BETWEEN '(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})' AND '(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})'"
                                relative_sql = re.sub(
                                    between_pattern,
                                    f"timestamp BETWEEN DATE_SUB(NOW(), INTERVAL {interval}) AND NOW()",
                                    sql,
                                )

                            # 生成描述性键名
                            filter_desc = []
                            if filter_config["appid_filter"]:
                                filter_desc.append(f"AppID={filter_config['appid_filter']}")
                            if filter_config["country_filter"]:
                                filter_desc.append(f"Country={filter_config['country_filter']}")
                            if digdimension:
                                filter_desc.append(f"Dig={digdimension}")
                            if where_condition:
                                filter_desc.append(
                                    f"Where={where_condition.replace(' ', '_').replace('=', 'eq').replace('\'', '')}"
                                )
                            filter_str = "_" + "_".join(filter_desc) if filter_desc else "_NoFilter"

                            key = f"{counter:03d}_{metric_name}_{metric_type}_{granularity}{filter_str}"
                            sql_combinations[key] = relative_sql

                            print(f"✓ 生成 SQL: {key}")

                        except Exception as e:
                            print(
                                f"✗ 生成失败: {metric_name} - {metric_type} - digdimension={digdimension} - where={where_condition} - {granularity} - {filter_config}: {e}"
                            )

    return sql_combinations


def save_to_sql_file(sql_combinations: Dict[str, str], filename: str = "all_sql.sql", data_dir: str = None):
    """
    保存 SQL 到文件，按指标分多个子文件夹，每个指标分多个文件

    Args:
        sql_combinations: SQL 组合字典
        filename: 输出文件名（用于向后兼容，实际会被忽略）
        data_dir: 数据目录路径，如果为None则使用默认的tests/data
    """
    if data_dir is None:
        data_dir = ensure_data_dir()

    # 按指标和类型分组SQL
    sql_by_metric = {}
    
    for key, sql in sql_combinations.items():
        # 解析key格式: {number}_{metric_name}_{metric_type}_{granularity}{filter_str}
        parts = key.split('_', 3)
        if len(parts) < 4:
            continue
            
        number, metric_name, metric_type, rest = parts
        
        # 进一步解析granularity和filter
        granularity_and_filter = rest
        
        # 判断SQL类型，优先级从高到低
        sql_type = "regular"  # 默认：常规查询
        
        if metric_type == "错误码分布":
            sql_type = "error_distribution"  # 错误码分布（最高优先级）
        elif "AppID=" in key and "Dig=country" in key:
            sql_type = "country_drill_with_appid"  # 指定appid的国家下钻
        elif "Dig=country" in key:
            sql_type = "country_drill"  # 国家下钻（无指定appid）
        elif "AppID=" in key:
            sql_type = "appid_filter"  # 仅appid过滤
        
        # 按指标分组
        if metric_name not in sql_by_metric:
            sql_by_metric[metric_name] = {
                "regular": [],
                "country_drill_with_appid": [],
                "country_drill": [],
                "appid_filter": [],
                "error_distribution": []
            }
        
        sql_by_metric[metric_name][sql_type].append((key, sql))

    # 为每个指标创建子文件夹和文件
    total_files = 0
    for metric_name, sql_groups in sql_by_metric.items():
        # 创建指标子文件夹
        metric_dir = os.path.join(data_dir, metric_name.replace('/', '_'))
        os.makedirs(metric_dir, exist_ok=True)
        
        # 保存不同类型的SQL到不同文件
        for sql_type, sql_list in sql_groups.items():
            if not sql_list:
                continue
                
            if sql_type == "regular":
                file_name = "regular_queries.sql"
                file_desc = "常规查询"
            elif sql_type == "country_drill_with_appid":
                file_name = "country_drill_with_appid.sql"
                file_desc = "指定AppID的国家下钻查询"
            elif sql_type == "country_drill":
                file_name = "country_drill.sql"
                file_desc = "国家下钻查询"
            elif sql_type == "appid_filter":
                file_name = "appid_filter.sql"
                file_desc = "AppID过滤查询"
            elif sql_type == "error_distribution":
                file_name = "error_distribution.sql"
                file_desc = "错误码分布查询"
            else:
                file_name = f"{sql_type}.sql"
                file_desc = sql_type
            
            filepath = os.path.join(metric_dir, file_name)
            
            with open(filepath, "w", encoding="utf-8") as f:
                f.write(f"-- {metric_name} - {file_desc}\n")
                f.write(f"-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"-- 总计 {len(sql_list)} 个 SQL 语句\n\n")

                for key, sql in sql_list:
                    f.write(f"-- {key}\n")
                    f.write(sql.strip())
                    f.write("\n\n")
            
            total_files += 1
            print(f"SQL 文件已保存到: {filepath} ({len(sql_list)} 条SQL)")

    print(f"总计保存了 {total_files} 个SQL文件，覆盖 {len(sql_by_metric)} 个指标")


def save_to_sqlite(sql_combinations: Dict[str, str], db_filename: str = "all_sql.sqlite", data_dir: str = None):
    """
    保存 SQL 到 SQLite 数据库

    Args:
        sql_combinations: SQL 组合字典
        db_filename: 数据库文件名
        data_dir: 数据目录路径，如果为None则使用默认的tests/data
    """
    if data_dir is None:
        data_dir = ensure_data_dir()

    db_path = os.path.join(data_dir, db_filename)

    # 检查是否存在旧文件，如果存在则对比
    old_exists = os.path.exists(db_path)

    if old_exists:
        # 读取旧数据进行对比
        old_data = {}
        try:
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT key, sql FROM sql_collection")
                old_data = dict(cursor.fetchall())
        except Exception as e:
            print(f"读取旧数据库失败: {e}")
            old_data = {}

        # 对比数据（增强版本，不中断测试）
        differences = []
        passed_count = 0
        failed_count = 0
        
        for key, sql in sql_combinations.items():
            if key in old_data:
                is_equal, diff_reason = compare_sql_strings(old_data[key], sql)
                if is_equal:
                    passed_count += 1
                    if diff_reason:  # 格式差异但语义相同
                        print(f"✓ {key}: {diff_reason}")
                else:
                    failed_count += 1
                    differences.append(f"CHANGED: {key} - {diff_reason}")
            else:
                differences.append(f"NEW: {key}")

        for key in old_data:
            if key not in sql_combinations:
                differences.append(f"REMOVED: {key}")

        print(f"对比结果: 通过 {passed_count} 个，差异 {failed_count} 个")

        if differences:
            print(f"检测到 {len(differences)} 处差异:")
            for diff in differences[:10]:  # 只显示前10个差异
                print(f"  {diff}")
            if len(differences) > 10:
                print(f"  ... 还有 {len(differences) - 10} 处差异")

            # 备份旧文件
            backup_name = f"{db_path}.old"
            if os.path.exists(backup_name):
                os.remove(backup_name)
            os.rename(db_path, backup_name)
            print(f"旧文件已备份为: {backup_name}")

            # 同时备份 SQL 文件
            sql_filename = "all_sql.sql"
            if os.path.exists(sql_filename):
                sql_backup_name = f"{sql_filename}.old"
                if os.path.exists(sql_backup_name):
                    os.remove(sql_backup_name)
                os.rename(sql_filename, sql_backup_name)
                print(f"旧 SQL 文件已备份为: {sql_backup_name}")
        else:
            print("数据无变化，跳过文件更新")
            return

    # 创建新数据库
    conn = sqlite3.connect(db_path)
    try:
        cursor = conn.cursor()

        # 创建表
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS sql_collection (
                key TEXT PRIMARY KEY,
                sql TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        )

        # 清空旧数据
        cursor.execute("DELETE FROM sql_collection")

        # 插入新数据
        for key, sql in sql_combinations.items():
            cursor.execute("INSERT INTO sql_collection (key, sql) VALUES (?, ?)", (key, sql))

        conn.commit()
    finally:
        conn.close()

    print(f"SQLite 数据库已保存到: {db_path}")


class TestFullSQLGeneration(unittest.TestCase):
    """全量 SQL 生成测试"""

    def test_generate_all_sql_combinations(self):
        """生成所有 SQL 组合的完整测试，包含变化检测"""
        data_dir = ensure_data_dir()
        sqlite_file = os.path.join(data_dir, "test_sqlgen.sqlite")
        old_data = {}

        # 如果存在旧的 SQLite 文件，读取数据用于对比
        if os.path.exists(sqlite_file):
            print(f"发现现有文件 {sqlite_file}，加载用于对比...")
            with sqlite3.connect(sqlite_file) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT key, sql FROM sql_collection")
                for key, sql in cursor.fetchall():
                    old_data[key] = sql

        # 生成所有 SQL 组合
        sql_combinations = generate_all_sql_combinations()

        # 验证生成的 SQL 数量
        self.assertGreater(len(sql_combinations), 500, "应该生成大量 SQL 语句")

        # 验证每个 SQL 都是有效的
        for key, sql in sql_combinations.items():
            with self.subTest(key=key):
                self.assertIsInstance(sql, str)
                self.assertGreater(len(sql), 0)
                self.assertIn("SELECT", sql.upper())
                self.assertIn("FROM", sql.upper())

        # 对比数据变化（不中断测试）
        changes_detected = False
        comparison_stats = {
            "passed": 0,
            "failed": 0,
            "added": 0,
            "removed": 0
        }

        if old_data:
            print(f"对比新旧数据...")

            # 检查删除的项
            removed_keys = set(old_data.keys()) - set(sql_combinations.keys())
            if removed_keys:
                comparison_stats["removed"] = len(removed_keys)
                print(f"检测到删除的 SQL: {len(removed_keys)} 个")
                changes_detected = True

            # 检查新增的项
            added_keys = set(sql_combinations.keys()) - set(old_data.keys())
            if added_keys:
                comparison_stats["added"] = len(added_keys)
                print(f"检测到新增的 SQL: {len(added_keys)} 个")
                changes_detected = True

            # 检查修改的项（使用新的比较方法）
            modified_keys = []
            for key in set(old_data.keys()) & set(sql_combinations.keys()):
                is_equal, diff_reason = compare_sql_strings(old_data[key], sql_combinations[key])
                if is_equal:
                    comparison_stats["passed"] += 1
                    if diff_reason:  # 格式差异但语义相同
                        print(f"✓ {key}: {diff_reason}")
                else:
                    comparison_stats["failed"] += 1
                    modified_keys.append((key, diff_reason))
                    changes_detected = True

            if modified_keys:
                print(f"检测到修改的 SQL: {len(modified_keys)} 个")
                for key, reason in modified_keys[:5]:  # 只显示前5个
                    print(f"  - {key}: {reason}")
                if len(modified_keys) > 5:
                    print(f"  ... 还有 {len(modified_keys) - 5} 个差异")

            print(f"对比统计: 通过 {comparison_stats['passed']} 个，失败 {comparison_stats['failed']} 个，新增 {comparison_stats['added']} 个，删除 {comparison_stats['removed']} 个")

            if not changes_detected:
                print("✅ 数据对比通过")
                return

        # 如果有变化或是首次生成，保存新数据
        if old_data and changes_detected:
            # 备份旧文件
            backup_file = sqlite_file + ".old"
            print(f"备份旧文件到 {backup_file}")
            import shutil

            shutil.copy2(sqlite_file, backup_file)

        # 保存新数据到 SQLite
        print(f"保存 {len(sql_combinations)} 个 SQL 到 {sqlite_file}")
        save_to_sqlite(sql_combinations, "test_sqlgen.sqlite", data_dir)
        save_to_sql_file(sql_combinations, "test_sqlgen.sql", data_dir)

        # 验证保存是否成功
        with sqlite3.connect(sqlite_file) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM sql_collection")
            count = cursor.fetchone()[0]
            self.assertEqual(count, len(sql_combinations))


def generate_daily_sql_main(data_dir: str = None):
    """生成日级别 SQL 的主函数"""
    if data_dir is None:
        data_dir = ensure_data_dir()

    print("开始生成日级别 SQL...")

    try:
        # 生成日级别 SQL
        sql_combinations = generate_daily_sql()

        print(f"\n总计生成 {len(sql_combinations)} 个日级别 SQL 语句")

        # 保存到文件
        save_to_sql_file(sql_combinations, "daily_sql.sql", data_dir)

        print("日级别 SQL 生成完成!")

    except Exception as e:
        print(f"执行失败: {e}")
        import traceback

        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="SQL 生成器测试工具")
    parser.add_argument("--data-dir", type=str, help="数据输出目录，默认为 tests/data")
    parser.add_argument(
        "action",
        choices=["generate-daily", "test"],
        help="执行的操作: generate-daily(生成日级别SQL), test(运行单元测试)",
    )

    args = parser.parse_args()

    if args.action == "generate-daily":
        exit(generate_daily_sql_main(args.data_dir))
    elif args.action == "test":
        unittest.main(argv=[""])
    else:
        parser.print_help()
        exit(1)
