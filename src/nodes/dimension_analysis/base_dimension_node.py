import logging
from typing import Literal, Optional

import pandas as pd
from langchain_core.messages import AIMessage, SystemMessage
from langchain_core.runnables import RunnableConfig
from langgraph.store.base import BaseStore
from langgraph.types import Command

from src.common.utils.llm_request_utils import create_token_update, structured_request
from src.common.utils.time_utils import get_prompt_timestamp
from src.nodes.common.data_center import DataCenter
from src.nodes.common.types import DimensionAnalysisInfo
from src.nodes.dimension_analysis.prompts import json_prompt_template
from src.state import State
from src.zego_tools import DataQueryParams, generate_sql
from src.zego_tools.sql_executor import execute_sql_for_llm

logger = logging.getLogger(__name__)


class BaseDimensionNode:
    """
    维度分析节点的基类，封装所有重复逻辑
    支持接受DataQueryParams参数进行独立的数据查询和分析
    """

    def __init__(
        self,
        prompt_template: str,
        query_params: Optional[DataQueryParams] = None,
        analysis_key: Optional[str] = None,
        analysis_name: Optional[str] = None,
    ):
        self.prompt_template = prompt_template
        self.query_params = query_params

        # 从query_params动态生成分析信息，或使用传入的值
        if query_params:
            from src.common.utils.dimension_utils import get_analysis_info_from_query_params

            self.analysis_key, self.analysis_name = get_analysis_info_from_query_params(query_params)
        else:
            # 使用传入的值或默认值
            self.analysis_key = analysis_key or "unknown_analysis"
            self.analysis_name = analysis_name or "未知分析"

        self.logger = logging.getLogger(f"{__name__}.{self.analysis_key}")

    async def execute(
        self, state: State, config: RunnableConfig, *, store: BaseStore
    ) -> Command[Literal["aggregator"]]:
        """
        执行维度分析的统一逻辑
        如果有query_params，则执行独立查询；否则使用DataCenter获取预查询数据
        """
        self.logger.info(f"🤖{self.analysis_name} node is working.")
        state_update = {"messages": []}

        # 初始化token计数变量
        input_tokens = 0
        output_tokens = 0

        # 获取数据
        if self.query_params:
            # 使用query_params执行独立查询
            data_df, data_prompt = await self._execute_independent_query()
        else:
            # 使用DataCenter获取预查询数据
            # 检查state中是否有指定的data_key（用于进一步分析）
            data_key = state.get("current_data_key", self.analysis_key)
            data_df, data_prompt = self._get_prequeried_data(store, config, data_key)

        # 检查数据状态并执行分析
        if data_df is None or data_df.empty:
            self.logger.warning(f"[{self.analysis_key}] 未获取到有效数据")
            analysis_info = self._create_default_analysis_info("未获取到有效数据")
        else:
            data_rows = len(data_df)
            # 检查数据量是否超过3000行
            if data_rows > 3000:
                self.logger.warning(f"[{self.analysis_key}] 数据量过多({data_rows}行)，超过3000行限制")
                analysis_info = self._create_default_analysis_info("数据过多请检查")
            else:
                # 构建提示词并请求LLM
                prompt_list = self._build_prompt_list(data_prompt)

                prompt = "\n".join(prompt_list)

                history_messages = state.get("messages", [])
                input_messages = [*history_messages, SystemMessage(content=prompt)]

                # 请求LLM
                response_raw, response_parsed, response_error, input_tokens, output_tokens = await structured_request(
                    self.analysis_key, input_messages, DimensionAnalysisInfo
                )
                state_update.update(create_token_update(input_tokens, output_tokens))
                analysis_info: DimensionAnalysisInfo = response_parsed

        self.logger.info(
            f"[{self.analysis_key}] ✅analysis_info = \n{analysis_info.model_dump_json(indent=4, exclude_none=False)}\n"
        )

        # 使用统一的消息生成方法
        dimension_message = analysis_info.to_message()
        dimension_message.additional_kwargs = {
            "input_tokens": input_tokens,
            "output_tokens": state_update,
            "thinking": analysis_info.thinking,
        }

        # 更新状态 - 使用统一的字典结构存储分析结果
        state_update["messages"].append(dimension_message)

        # 初始化dimension_analysis_results如果不存在
        current_results = state.get("dimension_analysis_results", {})
        current_results[self.analysis_key] = analysis_info
        state_update["dimension_analysis_results"] = current_results

        return Command(update=state_update, goto="aggregator")

    async def _execute_independent_query(self) -> tuple[pd.DataFrame, str]:
        """
        使用query_params执行独立查询

        Returns:
            tuple: (数据DataFrame, 数据提示词)
        """
        try:
            # 生成SQL并执行查询
            sql = generate_sql(self.query_params)
            result_df, error_info_text = await execute_sql_for_llm(sql)

            self.logger.info(f"[{self.analysis_key}] ✅ 独立查询完成: {len(result_df)} 条记录")

            # 构建数据提示词
            data_prompt = self._build_data_prompt(result_df, error_info_text, f"{self.analysis_name}数据")

            return result_df, data_prompt

        except Exception as e:
            self.logger.error(f"[{self.analysis_key}] ❌ 独立查询失败: {e}")
            return pd.DataFrame(), f"查询失败: {str(e)}"

    def _get_prequeried_data(
        self, store: BaseStore, config: RunnableConfig, data_key: str = None
    ) -> tuple[pd.DataFrame, str]:
        """
        从DataCenter获取预查询数据

        Args:
            store: 存储对象
            config: 运行配置
            data_key: 数据键，如果为None则使用self.dimension_key

        Returns:
            tuple: (数据DataFrame, 数据提示词)
        """
        if data_key is None:
            data_key = self.dimension_key

        data_entry = DataCenter.get_data(store, config, data_key)

        if not data_entry or data_entry.get("data") is None:
            return pd.DataFrame(), "未找到预查询的数据"

        data_df = data_entry.get("data")
        data_prompt = DataCenter.get_data_prompt(store, config, data_key, f"{self.analysis_name}数据")

        return data_df, data_prompt

    def _build_data_prompt(self, data_df: pd.DataFrame, error_info: str, title: str) -> str:
        """
        构建数据提示词

        Args:
            data_df: 数据DataFrame
            error_info: 错误信息
            title: 数据标题

        Returns:
            str: 构建的数据提示词
        """
        if data_df.empty:
            return f"**{title}**：\n数据为空或查询失败\n错误信息：{error_info}"

        # 构建数据描述
        data_description = f"**{title}**：\n"
        data_description += f"数据行数：{len(data_df)}\n"
        data_description += f"数据列：{', '.join(data_df.columns.tolist())}\n\n"

        # 添加数据预览（前10行）
        data_description += "**数据预览**：\n"
        data_description += data_df.head(10).to_string(index=False)

        # 如果有错误信息，添加到描述中
        if error_info and "错误码说明" in error_info:
            data_description += f"\n\n**错误码信息**：\n{error_info}"

        return data_description

    def _create_default_analysis_info(self, error_type: str = "未获取到有效数据") -> DimensionAnalysisInfo:
        """创建默认分析结果（当无数据或数据异常时）"""
        if error_type == "数据过多请检查":
            thinking = f"{self.analysis_name}数据量超过3000行，为保证分析质量和系统性能，请检查数据范围或调整查询条件"
            conclusion = f"由于{self.analysis_name}数据量过多（>3000行），请检查数据范围"
        else:
            thinking = f"未获取到有效的{self.analysis_name}数据，无法进行深度分析"
            conclusion = f"由于数据获取异常，无法完成{self.analysis_name}分析"

        return DimensionAnalysisInfo(
            thinking=thinking,
            dimension_name=self.analysis_name,
            analysis_conclusion=conclusion,
            found_issues=False,
            confidence_level="低",
        )

    def _build_prompt_list(self, data_prompt: str) -> list[str]:
        """
        构建提示词列表，支持动态模板参数
        """
        from src.common.utils.time_utils import get_prompt_timestamp

        prompt_list = []

        # 根据提示词模板的参数动态构建
        if hasattr(self.prompt_template, "input_variables"):
            template_vars = self.prompt_template.input_variables

            # 构建模板参数
            template_params = {"CURRENT_TIME": get_prompt_timestamp()}

            # 如果是维度分析模板，添加维度相关参数
            if "DIMENSION_NAME" in template_vars and self.query_params and self.query_params.digdimension:
                from src.common.utils.prompt_utils import get_dimension_prompt_params

                dimension_params = get_dimension_prompt_params(self.query_params)
                template_params.update(dimension_params)

            # 格式化提示词模板
            prompt_list.append(self.prompt_template.format(**template_params))
        else:
            # 简单的字符串模板
            prompt_list.append(self.prompt_template.format(CURRENT_TIME=get_prompt_timestamp()))

        # 添加JSON格式要求
        prompt_list.append(json_prompt_template.format(json_schema=DimensionAnalysisInfo.model_json_schema()))

        # 添加数据描述
        prompt_list.append(data_prompt)

        # 如果有query_params，添加查询参数说明
        if self.query_params:
            prompt_list.insert(-1, self.query_params.to_prompt())

        return prompt_list
