import logging
from typing import Dict, Literal

import pandas as pd
from langchain_core.messages import AIMessage, SystemMessage
from langchain_core.runnables import RunnableConfig
from langgraph.store.base import BaseStore
from langgraph.types import Send

from src.common.utils.llm_request_utils import create_token_update, structured_request
from src.common.utils.time_utils import get_prompt_timestamp
from src.nodes.common.data_center import DataCenter

from src.nodes.common.types import DispatcherInfo
from src.nodes.dispatcher.dispatcher_prompt import dispatcher_prompt_template, json_prompt_template
from src.state import State
from src.zego_tools import DataQueryParams, execute_sql_for_llm, generate_sql

logger = logging.getLogger(__name__)


async def zego_dispatcher_node(state: State, config: RunnableConfig, *, store: BaseStore):
    """
    问题分析和调度节点：分析用户问题，识别参数，决定需要启动哪些维度的并行分析任务，同时预先查询所需数据
    """
    logger.info("🤖zego_dispatcher node is working.")
    node_name = "zego_dispatcher"
    state_update = {"messages": []}

    # 构建提示词
    prompt_list = [
        dispatcher_prompt_template.format(
            CURRENT_TIME=get_prompt_timestamp(),
        ),
        json_prompt_template.format(
            json_schema=DispatcherInfo.model_json_schema(),
        ),
    ]
    prompt = "\n".join(prompt_list)

    # 输入消息
    history_messages = state.get("messages", [])
    input_messages = [*history_messages, SystemMessage(content=prompt)]

    # 请求LLM
    response_raw, response_parsed, response_error, input_tokens, output_tokens = await structured_request(
        node_name, input_messages, DispatcherInfo
    )
    state_update.update(create_token_update(input_tokens, output_tokens))

    # 处理响应
    dispatcher_info: DispatcherInfo = response_parsed
    logger.info(
        f"[{node_name}] ✅dispatcher_info = \n{dispatcher_info.model_dump_json(indent=4, exclude_none=False)}\n"
    )

    # 使用统一的消息生成方法
    state_update["messages"].append(dispatcher_info.to_message())

    # 批量执行数据查询
    logger.info(f"[{node_name}] 开始批量查询数据")
    success_count, total_count = await DataCenter.batch_query_and_store_data(
        store, config, dispatcher_info.query_params_list, node_name
    )

    # 打印查询结果日志
    if success_count == total_count:
        logger.info(f"[{node_name}] ✅ 批量查询完成: 成功 {success_count}/{total_count}")
    elif success_count > 0:
        logger.warning(f"[{node_name}] ⚠️ 批量查询部分成功: {success_count}/{total_count}")
    else:
        logger.error(f"[{node_name}] ❌ 批量查询全部失败: {success_count}/{total_count}")

    # 返回更新的状态，包含所有必要信息（数据已通过DataCenter存储）
    state_update.update({"dispatcher_info": dispatcher_info})

    return state_update





def dispatcher_router(state: State):
    """
    基于DataQueryParams直接路由，遍历query_params_list发送给worker节点
    """
    from langgraph.types import Send

    dispatcher_info = state.get("dispatcher_info")
    if not dispatcher_info:
        return "aggregator"

    # 如果没有查询参数，直接到aggregator
    if not dispatcher_info.query_params_list:
        return "aggregator"

    # 为每个query_param创建一个Send到worker节点
    send_list = []
    for query_param in dispatcher_info.query_params_list:
        # 创建包含查询参数的状态
        send_state = {
            **state,
            "current_query_params": query_param,
        }
        send_list.append(Send("worker", send_state))

    return send_list
