"""Define a custom Reasoning and Action agent.

Works with a chat model with tool calling support.
"""

from langgraph.checkpoint.memory import InMemorySaver
from langgraph.graph import StateGraph
from langgraph.store.memory import InMemoryStore

from src.nodes.aggregator.aggregator_node import aggregator_node, aggregator_router
from src.nodes.dimension_analysis.dimension_analysis_nodes import worker_node
from src.nodes.dispatcher.dispatcher_node import dispatcher_router, zego_dispatcher_node

# 节点导入（已删除 issue_init）

from src.state import State








zego_graph = (
    StateGraph(State)
    # 新的统一分析架构
    # 添加所有节点
    .set_entry_point("zego_dispatcher")
    .add_node("zego_dispatcher", zego_dispatcher_node)
    # 通用worker节点（处理所有类型的分析）
    .add_node("worker", worker_node)
    # 聚合节点
    .add_node("aggregator", aggregator_node)
    # 设置边
    # dispatcher使用conditional edge将query_params发送到worker节点
    .add_conditional_edges("zego_dispatcher", dispatcher_router)
    # worker节点完成后路由到aggregator
    .add_edge("worker", "aggregator")
    # aggregator节点使用条件边处理进一步分析
    .add_conditional_edges("aggregator", aggregator_router)
    # 设置结束点
    .set_finish_point("aggregator")
    # 编译图，包含Store配置
    .compile(
        name="zego_team",
        store=InMemoryStore(),
        # checkpointer=InMemorySaver(),  # 可选：添加checkpointer以支持持久化
    )
)
