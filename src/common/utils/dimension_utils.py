"""
维度分析工具函数：统一处理维度映射和分析信息生成
用于替代原有的复杂映射逻辑和固定的dimension_key
"""
from typing import Tuple
from src.zego_tools import DataQueryParams


def get_analysis_info_from_query_params(query_param: DataQueryParams) -> Tuple[str, str]:
    """
    从查询参数直接获取分析键和分析名称
    
    Args:
        query_param: 数据查询参数
        
    Returns:
        Tuple[str, str]: (analysis_key, analysis_name)
        - analysis_key: 用于节点路由和数据存储的键
        - analysis_name: 用于日志和LLM提示的显示名称
    """
    if query_param.metric_type == "错误码分布":
        analysis_key = "error_distribution_analysis"
        analysis_name = f"{query_param.metric_name}错误码分布分析"
    elif query_param.digdimension:
        # 维度下钻分析 - 支持任意维度，不再限定于固定的几个
        dimension_display_names = {
            "sdk_version": "SDK版本",
            "country": "国家",
            "isp": "运营商", 
            "app_id": "客户",
            "platform": "平台",
            "region": "地区",
            # 可以根据需要继续扩展，或者直接使用原始字段名
        }
        
        # 获取显示名称，如果没有映射则使用原始字段名
        display_name = dimension_display_names.get(query_param.digdimension, query_param.digdimension)
        
        analysis_key = f"{query_param.digdimension}_trend_analysis"
        analysis_name = f"{display_name}维度{query_param.metric_name}分析"
    else:
        # 普通趋势分析
        analysis_key = "metric_trend_analysis"
        analysis_name = f"{query_param.metric_name}趋势分析"
    
    return analysis_key, analysis_name


def get_node_name_from_analysis_key(analysis_key: str) -> str:
    """
    从分析键获取对应的节点名称
    
    Args:
        analysis_key: 分析键
        
    Returns:
        str: 节点名称
    """
    # 维度下钻分析统一映射到通用的维度分析节点
    if analysis_key.endswith("_trend_analysis") and analysis_key != "metric_trend_analysis":
        return "dimension_trend_analysis"
    
    # 其他分析类型的直接映射
    node_mapping = {
        "metric_trend_analysis": "metric_trend_analysis",
        "error_distribution_analysis": "error_distribution_analysis",
    }
    
    return node_mapping.get(analysis_key, analysis_key)


def get_dimension_display_name(digdimension: str) -> str:
    """
    获取维度的显示名称
    
    Args:
        digdimension: 维度字段名
        
    Returns:
        str: 维度的显示名称
    """
    dimension_display_names = {
        "sdk_version": "SDK版本",
        "country": "国家",
        "isp": "运营商", 
        "app_id": "客户",
        "platform": "平台",
        "region": "地区",
    }
    
    return dimension_display_names.get(digdimension, digdimension)


def get_query_title_from_params(query_param: DataQueryParams) -> str:
    """
    从查询参数生成查询标题
    
    Args:
        query_param: 数据查询参数
        
    Returns:
        str: 查询标题
    """
    if query_param.metric_type == "错误码分布":
        return "错误码分布分析数据"
    elif query_param.digdimension:
        display_name = get_dimension_display_name(query_param.digdimension)
        return f"{display_name}维度下钻分析数据"
    else:
        return "指标趋势分析数据"