"""
提示词工具函数：为统一的维度分析模板提供参数生成
"""

from src.zego_tools import DataQueryParams


def get_dimension_prompt_params(query_param: DataQueryParams) -> dict:
    """
    根据查询参数生成维度分析提示词的参数

    Args:
        query_param: 数据查询参数

    Returns:
        dict: 包含提示词模板参数的字典
    """
    # 维度上下文和特殊注释的映射
    dimension_contexts = {
        "sdk_version": "每个SDK版本代表不同的客户端实现。",
        "country": "每个国家代表不同的地理区域用户群体。",
        "isp": "每个运营商代表不同的网络接入服务提供商。",
        "app_id": "作为ToB公司，每个AppID代表一个客户的应用。",
        "platform": "每个平台代表不同的操作系统环境。",
        "region": "每个地区代表不同的地理位置用户群体。",
    }

    special_notes = {
        "isp": """
<注意>
沙特等地区存在运营商恶意封禁的情况，可能影响推拉流或登录成功率，这种情况错误码常见的有 12200100~12200106等
</注意>
""",
        "app_id": "考虑客户规模、重要程度等业务因素。",
    }

    # 获取维度显示名称
    from src.common.utils.dimension_utils import get_dimension_display_name

    if query_param.digdimension:
        dimension_name = get_dimension_display_name(query_param.digdimension)
        dimension_context = dimension_contexts.get(query_param.digdimension, "")
        special_note = special_notes.get(query_param.digdimension, "")
    else:
        # 非维度下钻的情况不应该调用这个函数，但为了安全起见提供默认值
        dimension_name = "指标"
        dimension_context = ""
        special_note = ""

    return {
        "DIMENSION_NAME": dimension_name,
        "DIMENSION_CONTEXT": dimension_context,
        "SPECIAL_NOTES": special_note,
    }
